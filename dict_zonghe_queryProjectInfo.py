#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
综合查询项目基本信息爬虫程序
对应接口：saleCenterApp/projectManage/queryProjectInfo
功能：从dict系统获取项目基本信息并同步到MySQL数据库
"""

import sys
import os
import json
import time
import requests
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
import pymysql
from datetime import datetime

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config, get_login_credentials

# 数据库配置
DB_CONFIG = get_db_config('default')

# API配置
API_URL = "https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectManage/queryProjectInfo"

def load_cookies():
    """从cookies.txt文件加载cookie"""
    try:
        with open('cookies.txt', 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)
        
        cookies = {}
        for cookie in cookie_data.get('cookies', []):
            cookies[cookie['name']] = cookie['value']
        
        print(f"[信息] 成功加载 {len(cookies)} 个Cookie")
        return cookies
    except Exception as e:
        print(f"[错误] 加载Cookie失败: {e}")
        return {}

def get_project_ids():
    """从v_distinct_project_id视图获取项目ID列表"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 查询v_distinct_project_id视图
        cursor.execute("SELECT project_id FROM v_distinct_project_id")
        project_ids = [row[0] for row in cursor.fetchall()]
        
        print(f"[信息] 从v_distinct_project_id视图获取到 {len(project_ids)} 个项目ID")
        return project_ids
        
    except Exception as e:
        print(f"[错误] 获取项目ID失败: {e}")
        # 如果视图不存在，尝试从其他表获取
        try:
            cursor.execute("SELECT DISTINCT `项目编码` FROM sign_data_detail WHERE `项目编码` IS NOT NULL AND `项目编码` != ''")
            project_ids = [row[0] for row in cursor.fetchall()]
            print(f"[信息] 从sign_data_detail表获取到 {len(project_ids)} 个项目ID")
            return project_ids
        except Exception as e2:
            print(f"[错误] 从备用表获取项目ID也失败: {e2}")
            return []
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def build_request_headers(cookies):
    """构建请求头"""
    cookie_str = "; ".join([f"{name}={value}" for name, value in cookies.items()])
    
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Host': 'dict.gmcc.net:30722',
        'Origin': 'https://dict.gmcc.net:30722',
        'Pragma': 'no-cache',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Cookie': cookie_str
    }
    return headers

def build_request_data(project_id, login_no):
    """构建请求数据"""
    return {
        "ROOT": {
            "HEADER": {
                "OPR_INFO": {
                    "LOGIN_NO": login_no
                }
            },
            "BODY": {
                "PROJECT_ID": project_id
            }
        }
    }

def query_project_info(project_id, cookies, login_no):
    """查询单个项目的基本信息"""
    try:
        headers = build_request_headers(cookies)
        data = build_request_data(project_id, login_no)
        
        response = requests.post(API_URL, headers=headers, json=data, timeout=30, verify=False)
        
        if response.status_code == 200:
            result = response.json()
            
            # 检查返回码
            return_code = result.get('ROOT', {}).get('BODY', {}).get('RETURN_CODE', '')
            if return_code == '0':
                return result.get('ROOT', {}).get('BODY', {}).get('OUT_DATA', {})
            else:
                error_msg = result.get('ROOT', {}).get('BODY', {}).get('RETURN_MSG', '未知错误')
                print(f"[警告] 项目 {project_id} 查询失败: {error_msg}")
                return None
        else:
            print(f"[错误] 项目 {project_id} HTTP请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"[错误] 项目 {project_id} 查询异常: {e}")
        return None

def save_to_database(project_data_list):
    """保存数据到数据库"""
    if not project_data_list:
        print("[警告] 没有数据需要保存")
        return
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 清空原有数据
        cursor.execute("DELETE FROM dict_zonghe_queryProjectInfo")
        print(f"[信息] 已清空原有数据")
        
        # 准备插入SQL
        insert_sql = """
        INSERT INTO dict_zonghe_queryProjectInfo (
            INPUT_LOGIN_NO, INPUT_PROJECT_ID, PROJECT_NO, PROJECT_NAME, BBOSS_PROJECT_ID,
            PROJECT_TYPE, PROJECT_TYPE_DESC, TRADE, TRADE_DESC, IS_BRING_INTO_PMO,
            PROJECT_CONTRACT_TYPE, PROJECT_CONTRACT_TYPE_NAME, IS_INVESTMENT_PROJECT,
            PROJECT_LABLE, PROJECT_LABLE_NAME, SALE_OPP_ID, CUST_ID, PROJECT_ID,
            ESTIMATED_CON_PERIOD, IS_CONNECTED_OPP, CUST_NAME, REGION_CODE, REGION_CODE_DESC,
            FIRST_SCENE, SECOND_SCENE, PROJECT_STAGE, PROJECT_STAGE_NAME, PROJECT_PROGRESS,
            PROJECT_PROGRESS_NAME, ESTIMATED_AMOUNT, PROJECT_SCOPE, REQUIREMENTS_TITEL,
            GROUP_ID, BUILD_MODE, BUILD_MODE_DESC, ELECTION_MODE, IS_BIG_PROJECT,
            IS_EXCELLENT_PROJECT, PARENT_PROJECT_ID, IS_SPEC_MARKET, IS_MAINTENANCE,
            PROJECT_STATUS, PROJECT_SOURCE, BID_FLAG, ORG_DESC, BID_FLAG_NAME, FLOW_TYPE,
            RUN_IP, REQUEST_ID, RETURN_CODE, RETURN_MSG, USER_MSG, DETAIL_MSG
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
            %s, %s
        )
        """
        
        # 批量插入数据
        for data in project_data_list:
            project_info = data['project_info']
            system_info = data['system_info']
            
            values = (
                data['input_login_no'], data['input_project_id'],
                project_info.get('PROJECT_NO'), project_info.get('PROJECT_NAME'), project_info.get('BBOSS_PROJECT_ID'),
                project_info.get('PROJECT_TYPE'), project_info.get('PROJECT_TYPE_DESC'), project_info.get('TRADE'), 
                project_info.get('TRADE_DESC'), project_info.get('IS_BRING_INTO_PMO'),
                project_info.get('PROJECT_CONTRACT_TYPE'), project_info.get('PROJECT_CONTRACT_TYPE_NAME'), 
                project_info.get('IS_INVESTMENT_PROJECT'), project_info.get('PROJECT_LABLE'), project_info.get('PROJECT_LABLE_NAME'),
                project_info.get('SALE_OPP_ID'), project_info.get('CUST_ID'), project_info.get('PROJECT_ID'),
                project_info.get('ESTIMATED_CON_PERIOD'), project_info.get('IS_CONNECTED_OPP'), project_info.get('CUST_NAME'),
                project_info.get('REGION_CODE'), project_info.get('REGION_CODE_DESC'), project_info.get('FIRST_SCENE'),
                project_info.get('SECOND_SCENE'), project_info.get('PROJECT_STAGE'), project_info.get('PROJECT_STAGE_NAME'),
                project_info.get('PROJECT_PROGRESS'), project_info.get('PROJECT_PROGRESS_NAME'), 
                project_info.get('ESTIMATED_AMOUNT'), project_info.get('PROJECT_SCOPE'), project_info.get('REQUIREMENTS_TITEL'),
                project_info.get('GROUP_ID'), project_info.get('BUILD_MODE'), project_info.get('BUILD_MODE_DESC'),
                project_info.get('ELECTION_MODE'), project_info.get('IS_BIG_PROJECT'), project_info.get('IS_EXCELLENT_PROJECT'),
                project_info.get('PARENT_PROJECT_ID'), project_info.get('IS_SPEC_MARKET'), project_info.get('IS_MAINTENANCE'),
                project_info.get('PROJECT_STATUS'), project_info.get('PROJECT_SOURCE'), project_info.get('BID_FLAG'),
                project_info.get('ORG_DESC'), project_info.get('BID_FLAG_NAME'), project_info.get('FLOW_TYPE'),
                system_info.get('RUN_IP'), system_info.get('REQUEST_ID'), system_info.get('RETURN_CODE'),
                system_info.get('RETURN_MSG'), system_info.get('USER_MSG'), system_info.get('DETAIL_MSG')
            )
            
            cursor.execute(insert_sql, values)
        
        conn.commit()
        print(f"[成功] 成功保存 {len(project_data_list)} 条项目基本信息数据")
        
    except Exception as e:
        print(f"[错误] 保存数据失败: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("=" * 60)
    print("综合查询项目基本信息爬虫程序启动")
    print("=" * 60)
    
    # 加载Cookie
    cookies = load_cookies()
    if not cookies:
        print("[错误] Cookie加载失败，请先运行 login2cookie.py 获取Cookie")
        return
    
    # 获取登录用户名
    login_no, _, _ = get_login_credentials()
    
    # 获取项目ID列表
    project_ids = get_project_ids()
    if not project_ids:
        print("[错误] 未获取到项目ID列表")
        return
    
    print(f"[信息] 开始处理 {len(project_ids)} 个项目")
    
    project_data_list = []
    success_count = 0
    error_count = 0
    
    for i, project_id in enumerate(project_ids, 1):
        print(f"[进度] {i}/{len(project_ids)} 处理项目: {project_id}")
        
        # 查询项目信息
        project_info = query_project_info(project_id, cookies, login_no)
        
        if project_info:
            # 提取系统信息
            system_info = {
                'RUN_IP': project_info.get('RUN_IP'),
                'REQUEST_ID': project_info.get('REQUEST_ID'),
                'RETURN_CODE': project_info.get('RETURN_CODE'),
                'RETURN_MSG': project_info.get('RETURN_MSG'),
                'USER_MSG': project_info.get('USER_MSG'),
                'DETAIL_MSG': project_info.get('DETAIL_MSG')
            }
            
            project_data_list.append({
                'input_login_no': login_no,
                'input_project_id': project_id,
                'project_info': project_info,
                'system_info': system_info
            })
            success_count += 1
        else:
            error_count += 1
        
        # 每处理10个项目休息一下
        if i % 10 == 0:
            time.sleep(0)
    
    print(f"\n[统计] 成功: {success_count}, 失败: {error_count}")
    
    # 保存到数据库
    if project_data_list:
        save_to_database(project_data_list)
    
    print("=" * 60)
    print("综合查询项目基本信息爬虫程序完成")
    print("=" * 60)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "-all":
        main()
    else:
        print("使用方法: python dict_zonghe_queryProjectInfo.py -all")
        print("说明: -all 参数表示轮询所有项目数据同步入库")
